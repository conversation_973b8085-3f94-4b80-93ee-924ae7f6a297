#!/usr/bin/env node

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

async function testWebApp() {
  console.log('🧪 开始测试 Web 应用...\n');

  // 检查是否安装了 puppeteer
  try {
    require.resolve('puppeteer');
  } catch (error) {
    console.log('❌ 需要安装 puppeteer 来进行自动化测试');
    console.log('   运行: npm install --save-dev puppeteer');
    console.log('   或者手动在浏览器中访问 http://localhost:3000 进行测试');
    return;
  }

  let browser;
  try {
    // 启动浏览器
    browser = await puppeteer.launch({ 
      headless: false, // 设置为 false 可以看到浏览器
      defaultViewport: { width: 1200, height: 800 }
    });
    
    const page = await browser.newPage();
    
    // 监听控制台输出
    page.on('console', msg => {
      const type = msg.type();
      if (type === 'error') {
        console.log(`❌ 控制台错误: ${msg.text()}`);
      } else if (type === 'warn') {
        console.log(`⚠️  控制台警告: ${msg.text()}`);
      } else if (msg.text().includes('Web polyfill initialized')) {
        console.log('✅ Web polyfill 初始化成功');
      }
    });

    // 监听页面错误
    page.on('pageerror', error => {
      console.log(`❌ 页面错误: ${error.message}`);
    });

    console.log('🌐 正在访问 http://localhost:3000...');
    
    // 访问页面
    await page.goto('http://localhost:3000', { 
      waitUntil: 'networkidle0',
      timeout: 30000 
    });

    // 等待应用加载
    await page.waitForSelector('#app', { timeout: 10000 });
    console.log('✅ 应用容器加载成功');

    // 检查标题
    const title = await page.title();
    console.log(`📄 页面标题: ${title}`);

    // 检查是否有主要组件
    const hasMainContent = await page.$('.app-container') !== null;
    if (hasMainContent) {
      console.log('✅ 主要内容容器存在');
    } else {
      console.log('❌ 主要内容容器不存在');
    }

    // 检查是否有导航菜单
    const hasNavigation = await page.$('.app-menu') !== null || await page.$('nav') !== null;
    if (hasNavigation) {
      console.log('✅ 导航菜单存在');
    } else {
      console.log('⚠️  导航菜单可能不存在或未加载');
    }

    // 检查是否有播放器组件
    const hasPlayer = await page.$('.play-bar') !== null || await page.$('[class*="play"]') !== null;
    if (hasPlayer) {
      console.log('✅ 播放器组件存在');
    } else {
      console.log('⚠️  播放器组件可能不存在或未加载');
    }

    // 截图
    const screenshotPath = path.join(__dirname, 'test-screenshot.png');
    await page.screenshot({ path: screenshotPath, fullPage: true });
    console.log(`📸 截图已保存到: ${screenshotPath}`);

    // 检查网络请求
    const responses = [];
    page.on('response', response => {
      if (response.status() >= 400) {
        responses.push({
          url: response.url(),
          status: response.status()
        });
      }
    });

    // 等待一段时间让所有请求完成
    await page.waitForTimeout(3000);

    if (responses.length > 0) {
      console.log('\n❌ 发现失败的网络请求:');
      responses.forEach(resp => {
        console.log(`   ${resp.status} - ${resp.url}`);
      });
    } else {
      console.log('✅ 所有网络请求都成功');
    }

    console.log('\n🎉 Web 应用测试完成！');
    console.log('💡 如果看到错误，请检查:');
    console.log('   1. API 服务器是否正常运行');
    console.log('   2. 环境变量配置是否正确');
    console.log('   3. 网络连接是否正常');

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// 简单的手动测试指南
function showManualTestGuide() {
  console.log('📋 手动测试指南:\n');
  console.log('1. 在浏览器中访问 http://localhost:3000');
  console.log('2. 检查页面是否正常加载');
  console.log('3. 检查控制台是否有错误信息');
  console.log('4. 尝试点击各个功能按钮');
  console.log('5. 检查音乐播放功能（需要配置 API）');
  console.log('\n🔧 如果遇到问题:');
  console.log('   - 检查 .env.production 中的 API 配置');
  console.log('   - 确保 API 服务器正常运行');
  console.log('   - 检查浏览器控制台的错误信息');
}

// 检查是否有 puppeteer
try {
  require.resolve('puppeteer');
  testWebApp();
} catch (error) {
  showManualTestGuide();
}

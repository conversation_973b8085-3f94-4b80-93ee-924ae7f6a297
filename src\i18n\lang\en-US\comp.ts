export default {
  installApp: {
    description: 'Install the application for a better experience',
    noPrompt: 'Do not prompt again',
    install: 'Install now',
    cancel: 'Cancel',
    download: 'Download',
    downloadFailed: 'Download failed',
    downloadComplete: 'Download complete',
    downloadProblem: 'Download problem? Go to',
    downloadProblemLinkText: 'Download the latest version'
  },
  playlistDrawer: {
    title: 'Add to playlist',
    createPlaylist: 'Create new playlist',
    cancelCreate: 'Cancel create',
    create: 'Create',
    playlistName: 'Playlist name',
    privatePlaylist: 'Private playlist',
    publicPlaylist: 'Public playlist',
    createSuccess: 'Playlist created successfully',
    createFailed: 'Playlist creation failed',
    addSuccess: 'Song added successfully',
    addFailed: 'Song addition failed',
    private: 'Private',
    public: 'Public',
    count: 'songs',
    loginFirst: 'Please login first',
    getPlaylistFailed: 'Get playlist failed',
    inputPlaylistName: 'Please enter the playlist name'
  },
  update: {
    title: 'New version found',
    currentVersion: 'Current version',
    cancel: 'Do not update',
    prepareDownload: 'Preparing to download...',
    downloading: 'Downloading...',
    nowUpdate: 'Update now',
    downloadFailed: 'Download failed, please try again or download manually',
    startFailed: 'Start download failed, please try again or download manually',
    noDownloadUrl:
      'No suitable installation package found for the current system, please download manually',
    installConfirmTitle: 'Install Update',
    installConfirmContent: 'Do you want to close the application and install the update?',
    manualInstallTip:
      'If the installer does not open automatically after closing the application, please find the file in your download folder and open it manually.',
    yesInstall: 'Install Now',
    noThanks: 'Later',
    fileLocation: 'File Location',
    copy: 'Copy Path',
    copySuccess: 'Path copied to clipboard',
    copyFailed: 'Copy failed',
    backgroundDownload: 'Background Download'
  },
  coffee: {
    title: 'Buy me a coffee',
    alipay: 'Alipay',
    wechat: 'Wechat',
    alipayQR: 'Alipay QR code',
    wechatQR: 'Wechat QR code',
    coffeeDesc: 'A cup of coffee, a support',
    coffeeDescLinkText: 'View more',
    qqGroup: 'QQ group: algermusic',
    messages: {
      copySuccess: 'Copied to clipboard'
    },
    donateList: 'Buy me a coffee'
  },
  playlistType: {
    title: 'Playlist Category',
    showAll: 'Show all',
    hide: 'Hide some'
  },
  recommendAlbum: {
    title: 'Latest Album'
  },
  recommendSinger: {
    title: 'Daily Recommendation',
    songlist: 'Daily Recommendation List'
  },
  recommendSonglist: {
    title: 'Weekly Hot Music'
  },
  searchBar: {
    login: 'Login',
    toLogin: 'To Login',
    logout: 'Logout',
    set: 'Settings',
    theme: 'Theme',
    restart: 'Restart',
    refresh: 'Refresh',
    currentVersion: 'Current Version',
    searchPlaceholder: 'Search for something...',
    zoom: 'Zoom',
    zoom100: 'Zoom 100%',
    resetZoom: 'Reset Zoom',
    zoomDefault: 'Default Zoom'
  },
  titleBar: {
    closeTitle: 'Choose how to close',
    minimizeToTray: 'Minimize to Tray',
    exitApp: 'Exit App',
    rememberChoice: 'Remember my choice',
    closeApp: 'Close App'
  },
  userPlayList: {
    title: "{name}'s Playlist"
  },
  musicList: {
    searchSongs: 'Search Songs',
    noSearchResults: 'No search results',
    switchToNormal: 'Switch to normal layout',
    switchToCompact: 'Switch to compact layout',
    playAll: 'Play All',
    collect: 'Collect',
    collectSuccess: 'Collect Success',
    cancelCollectSuccess: 'Cancel Collect Success',
    cancelCollect: 'Cancel Collect',
    addToPlaylist: 'Add to Playlist',
    addToPlaylistSuccess: 'Add to Playlist Success',
    operationFailed: 'Operation Failed',
    songsAlreadyInPlaylist: 'Songs already in playlist'
  },
  playlist: {
    import: {
      button: 'Import Playlist',
      title: 'Import Playlist',
      description: 'Import playlists via metadata, text, or links',
      linkTab: 'Import by Link',
      textTab: 'Import by Text',
      localTab: 'Import by Metadata',
      linkPlaceholder: 'Enter playlist links, one per line',
      textPlaceholder: 'Enter song information in format: Song Name Artist Name',
      localPlaceholder: 'Enter song metadata in JSON format',
      linkTips: 'Supported link sources:',
      linkTip1: 'Copy links after sharing playlists to WeChat/Weibo/QQ',
      linkTip2: 'Directly copy playlist/profile links',
      linkTip3: 'Directly copy article links',
      textTips: 'Enter song information, one song per line',
      textFormat: 'Format: Song Name Artist Name',
      localTips: 'Add song metadata',
      localFormat: 'Format example:',
      songNamePlaceholder: 'Song Name',
      artistNamePlaceholder: 'Artist Name',
      albumNamePlaceholder: 'Album Name',
      addSongButton: 'Add Song',
      addLinkButton: 'Add Link',
      importToStarPlaylist: 'Import to My Favorite Music',
      playlistNamePlaceholder: 'Enter playlist name',
      importButton: 'Start Import',
      emptyLinkWarning: 'Please enter playlist links',
      emptyTextWarning: 'Please enter song information',
      emptyLocalWarning: 'Please enter song metadata',
      invalidJsonFormat: 'Invalid JSON format',
      importSuccess: 'Import task created successfully',
      importFailed: 'Import failed',
      importStatus: 'Import Status',
      refresh: 'Refresh',
      taskId: 'Task ID',
      status: 'Status',
      successCount: 'Success Count',
      failReason: 'Failure Reason',
      unknownError: 'Unknown error',
      statusPending: 'Pending',
      statusProcessing: 'Processing',
      statusSuccess: 'Success',
      statusFailed: 'Failed',
      statusUnknown: 'Unknown',
      taskList: 'Task List',
      taskListTitle: 'Import Task List',
      action: 'Action',
      select: 'Select',
      fetchTaskListFailed: 'Failed to fetch task list',
      noTasks: 'No import tasks',
      clearTasks: 'Clear Tasks',
      clearTasksConfirmTitle: 'Confirm Clear',
      clearTasksConfirmContent: 'Are you sure you want to clear all import task records? This action cannot be undone.',
      confirm: 'Confirm',
      cancel: 'Cancel',
      clearTasksSuccess: 'Task list cleared',
      clearTasksFailed: 'Failed to clear task list'
    }
  },
  settings: 'Settings',
  user: 'User',
  toplist: 'Toplist',
  history: 'History',
  list: 'Playlist',
  mv: 'MV',
  home: 'Home',
  search: 'Search'
};

name: Deploy Web

on:
  push:
    branches:
      - main # 或者您的主分支名称
  workflow_dispatch: # 允许手动触发

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: 创建环境变量文件
        run: |
          echo "VITE_API=${{ secrets.VITE_API }}" > .env.production.local
          echo "VITE_API_MUSIC=${{ secrets.VITE_API_MUSIC }}" >> .env.production.local
          # 添加其他需要的环境变量
          cat .env.production.local # 查看创建的文件内容，调试用

      - name: Install Dependencies
        run: npm install

      - name: Build
        run: npm run build

      - name: Deploy to Server
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USERNAME }}
          key: ${{ secrets.DEPLOY_KEY }}
          source: "out/renderer/*"
          target: ${{ secrets.DEPLOY_PATH }}
          strip_components: 2

      - name: Execute Remote Commands
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USERNAME }}
          key: ${{ secrets.DEPLOY_KEY }}
          script: |
            cd ${{ secrets.DEPLOY_PATH }}
            echo "部署完成于 $(date)"
/**
 * Web 环境 Polyfill
 * 为 web 环境提供 Electron API 的兼容性支持
 */

// 创建一个空的 electron API 对象，避免运行时错误
const createElectronPolyfill = () => {
  const noop = () => {};
  const noopAsync = () => Promise.resolve();
  const noopSync = () => null;

  return {
    ipcRenderer: {
      send: noop,
      sendSync: noopSync,
      invoke: noopAsync,
      on: noop,
      once: noop,
      removeListener: noop,
      removeAllListeners: noop,
    }
  };
};

// 创建一个空的 api 对象
const createApiPolyfill = () => {
  const noop = () => {};
  const noopAsync = () => Promise.resolve();

  return {
    minimize: noop,
    maximize: noop,
    close: noop,
    dragStart: noop,
    miniTray: noop,
    miniWindow: noop,
    restore: noop,
    restart: noop,
    resizeWindow: noop,
    resizeMiniWindow: noop,
    openLyric: noop,
    sendLyric: noop,
    sendSong: noop,
    onLanguageChanged: noop,
    // 添加其他可能用到的 API
    selectDirectory: noopAsync,
    openDirectory: noop,
    downloadSong: noopAsync,
    getDownloadsPath: noopAsync,
    getStoreValue: noopSync,
    setStoreValue: noop,
  };
};

// 初始化 Web 环境的 polyfill
export const initWebPolyfill = () => {
  if (typeof window !== 'undefined' && !window.electron) {
    // 只在 web 环境且没有 electron 对象时添加 polyfill
    (window as any).electron = createElectronPolyfill();
    (window as any).api = createApiPolyfill();
    (window as any).ipcRenderer = createElectronPolyfill().ipcRenderer;
    
    console.log('🌐 Web polyfill initialized');
  }
};

// 检查是否为 web 环境
export const isWebEnvironment = () => {
  return typeof window !== 'undefined' && !window.electron;
};

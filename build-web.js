#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始构建 Web 版本...\n');

// 检查环境变量配置
const envFiles = ['.env.production', 'src/renderer/.env.production'];
let hasValidConfig = false;

for (const envFile of envFiles) {
  if (fs.existsSync(envFile)) {
    const content = fs.readFileSync(envFile, 'utf8');
    if (content.includes('your-api-domain.com') || content.includes('your-music-api-domain.com')) {
      console.log(`⚠️  警告: ${envFile} 中包含示例 API 地址，请替换为实际地址`);
    } else {
      hasValidConfig = true;
    }
  }
}

// 检查是否有本地环境变量文件
if (fs.existsSync('.env.production.local')) {
  console.log('✅ 发现本地生产环境配置文件 .env.production.local');
  hasValidConfig = true;
}

if (!hasValidConfig) {
  console.log('❌ 请先配置生产环境的 API 地址！');
  console.log('   1. 编辑 .env.production 文件');
  console.log('   2. 或创建 .env.production.local 文件');
  console.log('   详细说明请查看 CLOUDFLARE_DEPLOY.md');
  process.exit(1);
}

try {
  // 清理之前的构建
  if (fs.existsSync('dist')) {
    console.log('🧹 清理之前的构建文件...');
    fs.rmSync('dist', { recursive: true, force: true });
  }

  // 执行构建
  console.log('📦 正在构建...');
  execSync('npm run build:web', { stdio: 'inherit' });

  // 检查构建结果
  if (fs.existsSync('dist') && fs.existsSync('dist/index.html')) {
    console.log('\n✅ 构建成功！');
    console.log('📁 构建文件位于 dist 目录');
    console.log('🌐 你现在可以将 dist 文件夹上传到 Cloudflare Pages');
    
    // 显示文件大小信息
    const stats = fs.statSync('dist');
    console.log(`📊 构建目录大小: ${(getDirSize('dist') / 1024 / 1024).toFixed(2)} MB`);
    
    console.log('\n📖 部署说明请查看 CLOUDFLARE_DEPLOY.md');
  } else {
    console.log('❌ 构建失败，请检查错误信息');
    process.exit(1);
  }
} catch (error) {
  console.error('❌ 构建过程中出现错误:', error.message);
  process.exit(1);
}

function getDirSize(dirPath) {
  let size = 0;
  const files = fs.readdirSync(dirPath);
  
  for (const file of files) {
    const filePath = path.join(dirPath, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isDirectory()) {
      size += getDirSize(filePath);
    } else {
      size += stats.size;
    }
  }
  
  return size;
}

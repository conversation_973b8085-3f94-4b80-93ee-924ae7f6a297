# Cloudflare Pages 部署指南

本项目已经配置为支持 Cloudflare Pages 部署。按照以下步骤进行部署：

## 1. 配置环境变量

在部署前，你需要配置生产环境的 API 地址：

### 方法一：修改环境变量文件

编辑 `.env.production` 和 `src/renderer/.env.production` 文件，将以下地址替换为你的实际 API 地址：

```bash
# 你的接口地址 (必填) - 请替换为你的实际API地址
VITE_API = https://your-api-domain.com
# 音乐破解接口地址 web端 - 请替换为你的实际音乐API地址
VITE_API_MUSIC = https://your-music-api-domain.com
```

### 方法二：创建本地环境变量文件

创建 `.env.production.local` 文件（此文件不会被提交到 git）：

```bash
# .env.production.local
VITE_API = https://your-api-domain.com
VITE_API_MUSIC = https://your-music-api-domain.com
```

## 2. 构建项目

运行以下命令构建 web 版本：

```bash
npm install
npm run build:web
```

构建完成后，静态文件将生成在 `dist` 目录中。

## 3. 部署到 Cloudflare Pages

### 方法一：手动上传（推荐）

1. 登录 [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. 进入 Pages 页面
3. 点击 "Create a project"
4. 选择 "Upload assets"
5. 将 `dist` 文件夹拖拽上传
6. 设置项目名称并部署

### 方法二：Git 集成

1. 将代码推送到 GitHub/GitLab
2. 在 Cloudflare Pages 中连接你的仓库
3. 配置构建设置：
   - 构建命令：`npm run build:web`
   - 构建输出目录：`dist`
   - Node.js 版本：18 或更高

## 4. 注意事项

### API 服务器要求

由于这是一个音乐播放器应用，你需要部署以下服务：

1. **网易云音乐 API 服务**：
   - 推荐使用 [netease-cloud-music-api](https://github.com/Binaryify/NeteaseCloudMusicApi)
   - 可以部署到 Vercel、Railway 等平台

2. **音乐解锁服务**（可选）：
   - 如果需要音乐解锁功能，需要部署相应的解锁服务

### CORS 配置

确保你的 API 服务器配置了正确的 CORS 设置，允许来自 Cloudflare Pages 域名的请求。

### 域名配置

部署完成后，你可以在 Cloudflare Pages 中配置自定义域名。

## 5. 本地测试

在部署前，你可以本地测试构建结果：

```bash
# 构建项目
npm run build:web

# 使用简单的 HTTP 服务器测试
npx serve dist
```

## 6. 故障排除

### 常见问题

1. **API 请求失败**：
   - 检查环境变量配置是否正确
   - 确认 API 服务器是否正常运行
   - 检查 CORS 配置

2. **路由问题**：
   - 项目使用 Hash 路由模式，应该不会有路由问题
   - 如果遇到问题，检查 `src/renderer/router/index.ts` 配置

3. **资源加载失败**：
   - 检查 `vite.config.ts` 中的 `base` 配置
   - 确认静态资源路径正确

## 7. 快速部署脚本

为了简化部署流程，项目提供了以下便捷脚本：

```bash
# 构建并检查（推荐）
npm run build:web:check

# 仅构建
npm run build:web

# 测试构建结果
npm run test:build
```

## 8. 更新部署

当你需要更新应用时：

1. 修改代码
2. 重新构建：`npm run build:web:check`
3. 重新上传 `dist` 文件夹到 Cloudflare Pages

或者如果使用 Git 集成，直接推送代码即可自动重新部署。

## 9. 性能优化

构建后的应用已经进行了以下优化：

- **代码分割**：将代码分为 vendor、ui、utils 等块
- **Gzip 压缩**：所有资源都有对应的 .gz 文件
- **现代 JavaScript**：使用 esnext 目标，支持最新特性
- **资源预加载**：关键资源使用 modulepreload

构建后的总大小约为 6.26 MB（压缩前），实际传输大小会更小。

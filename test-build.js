#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🧪 测试构建结果...\n');

// 检查必要的文件
const requiredFiles = [
  'dist/index.html',
  'dist/favicon.ico',
  'dist/manifest.json'
];

const requiredDirs = [
  'dist/assets',
  'dist/icons'
];

let allGood = true;

// 检查文件
for (const file of requiredFiles) {
  if (fs.existsSync(file)) {
    const stats = fs.statSync(file);
    console.log(`✅ ${file} (${(stats.size / 1024).toFixed(2)} KB)`);
  } else {
    console.log(`❌ 缺少文件: ${file}`);
    allGood = false;
  }
}

// 检查目录
for (const dir of requiredDirs) {
  if (fs.existsSync(dir)) {
    const files = fs.readdirSync(dir);
    console.log(`✅ ${dir} (${files.length} 个文件)`);
  } else {
    console.log(`❌ 缺少目录: ${dir}`);
    allGood = false;
  }
}

// 检查 assets 目录中的关键文件
if (fs.existsSync('dist/assets')) {
  const assets = fs.readdirSync('dist/assets');
  const jsFiles = assets.filter(f => f.endsWith('.js'));
  const cssFiles = assets.filter(f => f.endsWith('.css'));
  
  console.log(`\n📦 Assets 统计:`);
  console.log(`   JavaScript 文件: ${jsFiles.length}`);
  console.log(`   CSS 文件: ${cssFiles.length}`);
  
  // 计算总大小
  let totalSize = 0;
  for (const file of assets) {
    if (!file.endsWith('.gz')) {
      const filePath = path.join('dist/assets', file);
      const stats = fs.statSync(filePath);
      totalSize += stats.size;
    }
  }
  
  console.log(`   总大小: ${(totalSize / 1024 / 1024).toFixed(2)} MB`);
}

// 检查 index.html 内容
if (fs.existsSync('dist/index.html')) {
  const html = fs.readFileSync('dist/index.html', 'utf8');
  
  console.log(`\n🔍 HTML 检查:`);
  
  if (html.includes('src="./assets/')) {
    console.log('✅ JavaScript 引用正确');
  } else {
    console.log('❌ JavaScript 引用可能有问题');
    allGood = false;
  }
  
  if (html.includes('href="./assets/') && html.includes('.css')) {
    console.log('✅ CSS 引用正确');
  } else {
    console.log('❌ CSS 引用可能有问题');
    allGood = false;
  }
  
  if (html.includes('href="./favicon.ico"')) {
    console.log('✅ Favicon 引用正确');
  } else {
    console.log('❌ Favicon 引用可能有问题');
  }
}

console.log(`\n${allGood ? '🎉' : '❌'} 构建测试${allGood ? '通过' : '失败'}！`);

if (allGood) {
  console.log('\n📋 部署步骤:');
  console.log('1. 将整个 dist 文件夹上传到 Cloudflare Pages');
  console.log('2. 或者压缩 dist 文件夹内容后上传');
  console.log('3. 确保已配置正确的 API 地址');
  console.log('\n📖 详细说明请查看 CLOUDFLARE_DEPLOY.md');
} else {
  console.log('\n🔧 请检查构建配置并重新构建');
}

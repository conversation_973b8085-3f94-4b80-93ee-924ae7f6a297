#!/usr/bin/env node

const http = require('http');
const fs = require('fs');

console.log('🧪 检查 Web 应用状态...\n');

// 检查本地服务器是否运行
function checkServer(port = 3000) {
  return new Promise((resolve) => {
    const req = http.get(`http://localhost:${port}`, (res) => {
      resolve({
        running: true,
        status: res.statusCode,
        headers: res.headers
      });
    });
    
    req.on('error', () => {
      resolve({ running: false });
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      resolve({ running: false });
    });
  });
}

// 检查构建文件
function checkBuildFiles() {
  const requiredFiles = [
    'dist/index.html',
    'dist/favicon.ico',
    'dist/manifest.json'
  ];

  const results = [];
  
  for (const file of requiredFiles) {
    if (fs.existsSync(file)) {
      const stats = fs.statSync(file);
      results.push({
        file,
        exists: true,
        size: stats.size
      });
    } else {
      results.push({
        file,
        exists: false
      });
    }
  }
  
  return results;
}

// 检查环境配置
function checkEnvironmentConfig() {
  const envFiles = [
    '.env.production',
    'src/renderer/.env.production',
    '.env.production.local'
  ];
  
  const results = [];
  
  for (const file of envFiles) {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8');
      const hasValidApi = !content.includes('your-api-domain.com') && 
                         !content.includes('your-music-api-domain.com');
      results.push({
        file,
        exists: true,
        configured: hasValidApi
      });
    } else {
      results.push({
        file,
        exists: false
      });
    }
  }
  
  return results;
}

async function main() {
  // 1. 检查构建文件
  console.log('📁 检查构建文件...');
  const buildFiles = checkBuildFiles();
  let buildOk = true;
  
  for (const result of buildFiles) {
    if (result.exists) {
      console.log(`✅ ${result.file} (${(result.size / 1024).toFixed(2)} KB)`);
    } else {
      console.log(`❌ ${result.file} - 文件不存在`);
      buildOk = false;
    }
  }
  
  if (!buildOk) {
    console.log('\n❌ 构建文件不完整，请运行: npm run build:web');
    return;
  }
  
  // 2. 检查环境配置
  console.log('\n🔧 检查环境配置...');
  const envConfigs = checkEnvironmentConfig();
  let hasValidConfig = false;
  
  for (const config of envConfigs) {
    if (config.exists) {
      if (config.configured) {
        console.log(`✅ ${config.file} - 已配置`);
        hasValidConfig = true;
      } else {
        console.log(`⚠️  ${config.file} - 包含示例配置，需要替换为实际 API 地址`);
      }
    } else {
      console.log(`⚪ ${config.file} - 不存在`);
    }
  }
  
  if (!hasValidConfig) {
    console.log('\n⚠️  警告: 没有找到有效的 API 配置');
    console.log('   应用可能无法正常连接到音乐服务');
    console.log('   请编辑 .env.production 或创建 .env.production.local');
  }
  
  // 3. 检查本地服务器
  console.log('\n🌐 检查本地服务器...');
  const serverStatus = await checkServer();
  
  if (serverStatus.running) {
    console.log(`✅ 服务器运行中 (状态码: ${serverStatus.status})`);
    console.log('🔗 访问地址: http://localhost:3000');
    
    // 检查内容类型
    if (serverStatus.headers['content-type']?.includes('text/html')) {
      console.log('✅ 正确返回 HTML 内容');
    }
  } else {
    console.log('❌ 服务器未运行');
    console.log('   请运行: npx serve dist');
  }
  
  // 4. 总结和建议
  console.log('\n📋 测试建议:');
  console.log('1. 在浏览器中访问 http://localhost:3000');
  console.log('2. 打开浏览器开发者工具 (F12)');
  console.log('3. 查看 Console 标签页是否有错误');
  console.log('4. 查看 Network 标签页检查资源加载');
  console.log('5. 尝试使用应用的各项功能');
  
  if (hasValidConfig) {
    console.log('\n✅ 应用应该能够正常运行');
  } else {
    console.log('\n⚠️  应用可能无法连接到音乐服务，但界面应该能正常显示');
  }
  
  console.log('\n🚀 部署准备:');
  if (buildOk && hasValidConfig) {
    console.log('✅ 可以部署到 Cloudflare Pages');
    console.log('   上传 dist 文件夹的内容即可');
  } else {
    console.log('❌ 请先解决上述问题再进行部署');
  }
}

main().catch(console.error);
